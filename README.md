# Bakong Transaction Status Telegram Bot

A Telegram bot that checks transaction status using MD5 hash via the Bakong API with Bearer token authentication.

## Features

- ✅ Check transaction status by MD5 hash
- 🔐 Bearer token authentication support
- 🤖 User-friendly Telegram interface
- 📱 Inline keyboard navigation
- 🔍 Multiple API endpoint fallback
- 📊 JSON formatted transaction details
- ❌ Comprehensive error handling

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Create Telegram Bot

1. Message [@<PERSON>t<PERSON>ather](https://t.me/BotFather) on Telegram
2. Send `/newbot` and follow instructions
3. Save the bot token you receive

### 3. Set Environment Variables

#### Windows (Command Prompt):
```cmd
set TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
set BAKONG_BEARER_TOKEN=your_bakong_bearer_token_here
```

#### Windows (PowerShell):
```powershell
$env:TELEGRAM_BOT_TOKEN="your_telegram_bot_token_here"
$env:BAKONG_BEARER_TOKEN="your_bakong_bearer_token_here"
```

#### Linux/Mac:
```bash
export TELEGRAM_BOT_TOKEN="your_telegram_bot_token_here"
export BAKONG_BEARER_TOKEN="your_bakong_bearer_token_here"
```

### 4. Run the Bot

```bash
python bakong_telegram_bot.py
```

## Usage

### Commands

- `/start` - Show welcome message and main menu
- `/help` - Display help information
- `/check <md5_hash>` - Check specific transaction status

### Direct MD5 Input

You can also send MD5 hashes directly to the bot without any command:

```
a1b2c3d4e5f6789012345678901234ab
```

### MD5 Hash Format

- Must be exactly 32 characters
- Contains only hexadecimal characters (0-9, a-f)
- Case insensitive

## API Endpoints

The bot tries multiple common endpoint patterns for the Bakong API:

1. `/api/v1/transaction/status/md5/{md5_hash}`
2. `/api/transaction/check/md5/{md5_hash}`
3. `/v1/transaction/status/{md5_hash}`
4. `/api/check-transaction-by-md5/{md5_hash}`

## Configuration

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `TELEGRAM_BOT_TOKEN` | Yes | Your Telegram bot token from @BotFather |
| `BAKONG_BEARER_TOKEN` | No | Bearer token for Bakong API authentication |

### API Configuration

You can modify the Bakong API base URL in the code:

```python
# Default: https://api-bakong.nbc.gov.kh
api = BakongAPI(base_url="https://your-custom-api-url.com")
```

## Error Handling

The bot handles various error scenarios:

- ❌ Invalid MD5 hash format
- 🔍 Transaction not found
- 🚨 API connection errors
- 🔐 Authentication failures
- 📡 Network timeouts

## Example Responses

### Successful Transaction Check
```
✅ Transaction Found

🔗 MD5: a1b2c3d4e5f6789012345678901234ab
📡 Endpoint: /api/v1/transaction/status/md5/a1b2c3d4e5f6789012345678901234ab

📊 Transaction Details:
{
  "status": "completed",
  "amount": "100.00",
  "currency": "KHR",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Transaction Not Found
```
❌ Transaction Not Found

🔗 MD5: a1b2c3d4e5f6789012345678901234ab

💡 Possible reasons:
• Transaction doesn't exist
• Invalid MD5 hash
• API endpoint not accessible
• Authentication issues
```

## Development

### Project Structure

```
├── bakong_telegram_bot.py  # Main bot application
├── requirements.txt        # Python dependencies
└── README.md              # This file
```

### Key Classes

- `BakongAPI`: Handles API communication with Bakong
- `BakongTelegramBot`: Manages Telegram bot functionality

### Adding New Features

1. Extend the `BakongAPI` class for new endpoints
2. Add new command handlers in `BakongTelegramBot`
3. Update the help messages and documentation

## Troubleshooting

### Common Issues

1. **Bot not responding**: Check if `TELEGRAM_BOT_TOKEN` is set correctly
2. **API errors**: Verify `BAKONG_BEARER_TOKEN` and network connectivity
3. **Invalid MD5**: Ensure hash is exactly 32 hexadecimal characters

### Logs

The bot logs important events. Check console output for debugging information.

## Security Notes

- Keep your bot token secure and never share it publicly
- Store bearer tokens as environment variables, not in code
- Consider rate limiting for production use
- Validate all user inputs

## License

This project is provided as-is for educational and development purposes.

## Support

For issues related to:
- **Telegram Bot**: Check Telegram Bot API documentation
- **Bakong API**: Contact NBC (National Bank of Cambodia)
- **This Code**: Review the code comments and error messages
