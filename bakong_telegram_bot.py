#!/usr/bin/env python3
"""
Bakong Transaction Status Telegram Bot - HTTP Version
Simple HTTP-based bot that works without complex dependencies
"""

import requests
import json
import time
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Configuration
TELEGRAM_TOKEN = "**********************************************"
BAKONG_BEARER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k"
BAKONG_API_URL = "https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5"
TELEGRAM_API_URL = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}"

class BakongTelegramBot:
    def __init__(self):
        self.last_update_id = 0
    
    def check_transaction_by_md5(self, md5_hash: str) -> Dict[str, Any]:
        """Check transaction status by MD5 hash"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {BAKONG_BEARER_TOKEN}"
        }
        
        payload = {"md5": md5_hash}
        
        try:
            logger.info(f"Checking transaction: {md5_hash}")
            response = requests.post(BAKONG_API_URL, headers=headers, json=payload, timeout=30)
            
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response text: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json() if response.text else {}
                    return {"success": True, "data": data, "status_code": response.status_code}
                except json.JSONDecodeError:
                    return {"success": True, "data": {"raw_response": response.text}, "status_code": response.status_code}
            else:
                return {
                    "success": False,
                    "error": f"API returned status {response.status_code}: {response.text}",
                    "status_code": response.status_code
                }
        except Exception as e:
            logger.error(f"Error calling API: {str(e)}")
            return {"success": False, "error": f"Network error: {str(e)}", "status_code": None}
    
    def is_valid_md5(self, text: str) -> bool:
        """Validate MD5 hash format"""
        if len(text) != 32:
            return False
        try:
            int(text, 16)
            return True
        except ValueError:
            return False
    
    def send_message(self, chat_id: int, text: str, parse_mode: str = "Markdown", reply_markup: Dict = None):
        """Send message via Telegram API"""
        url = f"{TELEGRAM_API_URL}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode
        }
        if reply_markup:
            data["reply_markup"] = json.dumps(reply_markup)
        
        try:
            response = requests.post(url, json=data, timeout=10)
            return response.json()
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return None
    
    def edit_message(self, chat_id: int, message_id: int, text: str, parse_mode: str = "Markdown"):
        """Edit message via Telegram API"""
        url = f"{TELEGRAM_API_URL}/editMessageText"
        data = {
            "chat_id": chat_id,
            "message_id": message_id,
            "text": text,
            "parse_mode": parse_mode
        }
        
        try:
            response = requests.post(url, json=data, timeout=10)
            return response.json()
        except Exception as e:
            logger.error(f"Error editing message: {str(e)}")
            return None
    
    def get_updates(self):
        """Get updates from Telegram"""
        url = f"{TELEGRAM_API_URL}/getUpdates"
        params = {
            "offset": self.last_update_id + 1,
            "timeout": 10
        }
        
        try:
            response = requests.get(url, params=params, timeout=15)
            return response.json()
        except Exception as e:
            logger.error(f"Error getting updates: {str(e)}")
            return None
    
    def handle_start_command(self, chat_id: int):
        """Handle /start command"""
        welcome_message = (
            "🏦 *Bakong Transaction Status Bot*\n\n"
            "Welcome! I can help you check transaction status using MD5 hash.\n\n"
            "*Commands:*\n"
            "• `/start` - Show this welcome message\n"
            "• `/check <md5_hash>` - Check transaction status\n"
            "• `/help` - Show help information\n\n"
            "*Usage:*\n"
            "Send me an MD5 hash directly or use the `/check` command.\n\n"
            "Example: `/check a1b2c3d4e5f6789012345678901234ab`"
        )
        
        reply_markup = {
            "inline_keyboard": [
                [{"text": "📋 Help", "callback_data": "help"}],
                [{"text": "🔍 Sample", "callback_data": "sample"}]
            ]
        }
        
        self.send_message(chat_id, welcome_message, reply_markup=reply_markup)
    
    def handle_help_command(self, chat_id: int):
        """Handle /help command"""
        help_message = (
            "🆘 *Help - Bakong Transaction Status Bot*\n\n"
            "*How to use:*\n"
            "1. Send me an MD5 hash (32 characters)\n"
            "2. Or use `/check <md5_hash>`\n\n"
            "*MD5 Hash Format:*\n"
            "• Must be exactly 32 characters\n"
            "• Contains only letters (a-f) and numbers (0-9)\n"
            "• Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
            "💡 *Tip:* You can send the MD5 hash directly!"
        )
        self.send_message(chat_id, help_message)
    
    def handle_check_command(self, chat_id: int, args: str):
        """Handle /check command"""
        if not args.strip():
            self.send_message(
                chat_id,
                "❌ Please provide an MD5 hash.\n\n"
                "Usage: `/check <md5_hash>`\n"
                "Example: `/check a1b2c3d4e5f6789012345678901234ab`"
            )
            return
        
        md5_hash = args.strip()
        self.process_transaction_check(chat_id, md5_hash)
    
    def process_transaction_check(self, chat_id: int, md5_hash: str):
        """Process transaction status check"""
        if not self.is_valid_md5(md5_hash):
            self.send_message(
                chat_id,
                "❌ Invalid MD5 hash format. Please provide a valid 32-character MD5 hash."
            )
            return
        
        # Send "checking" message
        checking_response = self.send_message(
            chat_id,
            f"🔍 Checking transaction status...\n\nMD5: `{md5_hash}`"
        )
        
        if not checking_response or not checking_response.get("ok"):
            return
        
        message_id = checking_response["result"]["message_id"]
        
        try:
            result = self.check_transaction_by_md5(md5_hash)
            
            if result["success"]:
                data = result["data"]
                response_message = (
                    f"✅ *Transaction Found*\n\n"
                    f"🔗 MD5: `{md5_hash}`\n\n"
                    f"📊 *Transaction Details:*\n"
                    f"```json\n{json.dumps(data, indent=2, ensure_ascii=False)}\n```"
                )
            else:
                response_message = (
                    f"❌ *Transaction Not Found*\n\n"
                    f"🔗 MD5: `{md5_hash}`\n\n"
                    f"💡 *Possible reasons:*\n"
                    f"• Transaction doesn't exist\n"
                    f"• Invalid MD5 hash\n"
                    f"• API endpoint not accessible\n"
                    f"• Authentication issues\n\n"
                    f"Error: {result.get('error', 'Unknown error')}"
                )
            
            self.edit_message(chat_id, message_id, response_message)
            
        except Exception as e:
            error_message = (
                f"🚨 *Error Occurred*\n\n"
                f"🔗 MD5: `{md5_hash}`\n\n"
                f"❌ Error: {str(e)}\n\n"
                f"Please try again later."
            )
            self.edit_message(chat_id, message_id, error_message)
            logger.error(f"Error checking transaction {md5_hash}: {str(e)}")
    
    def handle_message(self, chat_id: int, text: str):
        """Handle regular messages"""
        text = text.strip()
        
        if text.startswith('/start'):
            self.handle_start_command(chat_id)
        elif text.startswith('/help'):
            self.handle_help_command(chat_id)
        elif text.startswith('/check'):
            args = text[6:].strip()  # Remove '/check' prefix
            self.handle_check_command(chat_id, args)
        elif self.is_valid_md5(text):
            self.process_transaction_check(chat_id, text)
        else:
            self.send_message(
                chat_id,
                "❌ Invalid MD5 hash format.\n\n"
                "MD5 hash should be:\n"
                "• Exactly 32 characters\n"
                "• Contains only letters (a-f) and numbers (0-9)\n\n"
                "Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
                "Use `/help` for more information."
            )
    
    def run(self):
        """Run the bot"""
        logger.info("Starting Bakong Transaction Status Bot (HTTP version)...")
        logger.info("Bot is running... Press Ctrl+C to stop.")
        
        while True:
            try:
                updates = self.get_updates()
                
                if updates and updates.get("ok"):
                    for update in updates["result"]:
                        self.last_update_id = update["update_id"]
                        
                        if "message" in update:
                            message = update["message"]
                            chat_id = message["chat"]["id"]
                            
                            if "text" in message:
                                text = message["text"]
                                logger.info(f"Received message from {chat_id}: {text}")
                                self.handle_message(chat_id, text)
                
                time.sleep(1)  # Small delay to avoid overwhelming the API
                
            except KeyboardInterrupt:
                logger.info("Bot stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in main loop: {str(e)}")
                time.sleep(5)  # Wait before retrying

def main():
    """Main function"""
    if not TELEGRAM_TOKEN or TELEGRAM_TOKEN == "YOUR_TELEGRAM_BOT_TOKEN_HERE":
        print("❌ Error: Please set your Telegram bot token!")
        return
    
    try:
        bot = BakongTelegramBot()
        bot.run()
    except Exception as e:
        logger.error(f"Bot error: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
