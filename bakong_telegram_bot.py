#!/usr/bin/env python3
"""
Bakong Transaction Status Telegram Bot
Checks transaction status using MD5 hash via Bakong API
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    CallbackQueryHandler,
    ContextTypes,
    filters
)

# Try to import config, fallback to environment variables
try:
    from config import TELEGRAM_BOT_TOKEN, BAKONG_BEARER_TOKEN, BAKONG_API_URL
    CONFIG_FILE_AVAILABLE = True
except ImportError:
    CONFIG_FILE_AVAILABLE = False
    TELEGRAM_BOT_TOKEN = None
    BAKONG_BEARER_TOKEN = None
    BAKONG_API_URL = "https://api-bakong.nbc.gov.kh"

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BakongAPI:
    """Bakong API client for transaction status checking"""
    
    def __init__(self, base_url: str = "https://api-bakong.nbc.gov.kh", bearer_token: str = None):
        self.base_url = base_url
        self.bearer_token = bearer_token
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_transaction_by_md5(self, md5_hash: str) -> Dict[str, Any]:
        """
        Check transaction status by MD5 hash

        Args:
            md5_hash: MD5 hash of the transaction

        Returns:
            Dict containing transaction status information
        """
        if not self.session:
            raise RuntimeError("API client not initialized. Use async context manager.")

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        if self.bearer_token:
            headers["Authorization"] = f"Bearer {self.bearer_token}"

        # Use the correct Bakong API endpoint
        endpoint = "/v1/check_transaction_by_md5"
        url = f"{self.base_url}{endpoint}"

        # Prepare the request payload
        payload = {
            "md5": md5_hash
        }

        try:
            logger.info(f"Checking transaction with endpoint: {url}")
            logger.info(f"Payload: {payload}")

            async with self.session.post(url, headers=headers, json=payload) as response:
                response_text = await response.text()
                logger.info(f"Response status: {response.status}")
                logger.info(f"Response text: {response_text}")

                if response.status == 200:
                    try:
                        data = await response.json() if response_text else {}
                        return {
                            "success": True,
                            "data": data,
                            "endpoint_used": endpoint,
                            "status_code": response.status
                        }
                    except json.JSONDecodeError:
                        return {
                            "success": True,
                            "data": {"raw_response": response_text},
                            "endpoint_used": endpoint,
                            "status_code": response.status
                        }
                else:
                    return {
                        "success": False,
                        "error": f"API returned status {response.status}: {response_text}",
                        "status_code": response.status,
                        "endpoint_used": endpoint
                    }

        except Exception as e:
            logger.error(f"Error calling API endpoint {endpoint}: {str(e)}")
            return {
                "success": False,
                "error": f"Network error: {str(e)}",
                "status_code": None,
                "endpoint_used": endpoint
            }

class BakongTelegramBot:
    """Telegram bot for Bakong transaction status checking"""
    
    def __init__(self, telegram_token: str, bakong_bearer_token: str = None):
        self.telegram_token = telegram_token
        self.bakong_bearer_token = bakong_bearer_token
        self.application = None
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = (
            "🏦 *Bakong Transaction Status Bot*\n\n"
            "Welcome! I can help you check transaction status using MD5 hash.\n\n"
            "*Commands:*\n"
            "• `/start` - Show this welcome message\n"
            "• `/check <md5_hash>` - Check transaction status\n"
            "• `/help` - Show help information\n\n"
            "*Usage:*\n"
            "Send me an MD5 hash directly or use the `/check` command followed by the MD5 hash.\n\n"
            "Example: `/check a1b2c3d4e5f6789012345678901234ab`"
        )
        
        keyboard = [
            [InlineKeyboardButton("📋 Help", callback_data="help")],
            [InlineKeyboardButton("🔍 Check Sample", callback_data="sample")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = (
            "🆘 *Help - Bakong Transaction Status Bot*\n\n"
            "*How to use:*\n"
            "1. Send me an MD5 hash (32 characters)\n"
            "2. Or use `/check <md5_hash>`\n\n"
            "*MD5 Hash Format:*\n"
            "• Must be exactly 32 characters\n"
            "• Contains only letters (a-f) and numbers (0-9)\n"
            "• Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
            "*Commands:*\n"
            "• `/start` - Welcome message\n"
            "• `/check <hash>` - Check specific transaction\n"
            "• `/help` - This help message\n\n"
            "💡 *Tip:* You can send the MD5 hash directly without any command!"
        )
        
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def check_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /check command"""
        if not context.args:
            await update.message.reply_text(
                "❌ Please provide an MD5 hash.\n\n"
                "Usage: `/check <md5_hash>`\n"
                "Example: `/check a1b2c3d4e5f6789012345678901234ab`",
                parse_mode='Markdown'
            )
            return
        
        md5_hash = context.args[0].strip()
        await self.process_transaction_check(update, md5_hash)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle direct MD5 hash messages"""
        message_text = update.message.text.strip()
        
        # Check if message looks like an MD5 hash
        if self.is_valid_md5(message_text):
            await self.process_transaction_check(update, message_text)
        else:
            await update.message.reply_text(
                "❌ Invalid MD5 hash format.\n\n"
                "MD5 hash should be:\n"
                "• Exactly 32 characters\n"
                "• Contains only letters (a-f) and numbers (0-9)\n\n"
                "Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
                "Use `/help` for more information.",
                parse_mode='Markdown'
            )
    
    def is_valid_md5(self, text: str) -> bool:
        """Validate MD5 hash format"""
        if len(text) != 32:
            return False
        
        try:
            int(text, 16)  # Check if it's valid hexadecimal
            return True
        except ValueError:
            return False
    
    async def process_transaction_check(self, update: Update, md5_hash: str):
        """Process transaction status check"""
        if not self.is_valid_md5(md5_hash):
            await update.message.reply_text(
                "❌ Invalid MD5 hash format. Please provide a valid 32-character MD5 hash.",
                parse_mode='Markdown'
            )
            return
        
        # Send "checking" message
        checking_msg = await update.message.reply_text(
            f"🔍 Checking transaction status...\n\n"
            f"MD5: `{md5_hash}`",
            parse_mode='Markdown'
        )
        
        try:
            async with BakongAPI(bearer_token=self.bakong_bearer_token) as api:
                result = await api.check_transaction_by_md5(md5_hash)
                
                if result["success"]:
                    # Format successful response
                    data = result["data"]
                    response_message = (
                        f"✅ *Transaction Found*\n\n"
                        f"🔗 MD5: `{md5_hash}`\n"
                        f"📡 Endpoint: `{result['endpoint_used']}`\n\n"
                        f"📊 *Transaction Details:*\n"
                        f"```json\n{json.dumps(data, indent=2, ensure_ascii=False)}\n```"
                    )
                else:
                    # Format error response
                    response_message = (
                        f"❌ *Transaction Not Found*\n\n"
                        f"🔗 MD5: `{md5_hash}`\n\n"
                        f"💡 *Possible reasons:*\n"
                        f"• Transaction doesn't exist\n"
                        f"• Invalid MD5 hash\n"
                        f"• API endpoint not accessible\n"
                        f"• Authentication issues\n\n"
                        f"Error: {result.get('error', 'Unknown error')}"
                    )
                
                await checking_msg.edit_text(response_message, parse_mode='Markdown')
                
        except Exception as e:
            error_message = (
                f"🚨 *Error Occurred*\n\n"
                f"🔗 MD5: `{md5_hash}`\n\n"
                f"❌ Error: {str(e)}\n\n"
                f"Please try again later or contact support."
            )
            await checking_msg.edit_text(error_message, parse_mode='Markdown')
            logger.error(f"Error checking transaction {md5_hash}: {str(e)}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button callbacks"""
        query = update.callback_query
        await query.answer()
        
        if query.data == "help":
            await self.help_command(update, context)
        elif query.data == "sample":
            sample_message = (
                "📝 *Sample MD5 Hash*\n\n"
                "Here's a sample MD5 hash format:\n"
                "`a1b2c3d4e5f6789012345678901234ab`\n\n"
                "You can copy this and modify it, or use your actual transaction MD5 hash.\n\n"
                "💡 Just send the MD5 hash directly to check!"
            )
            await query.edit_message_text(sample_message, parse_mode='Markdown')
    
    def run(self):
        """Run the Telegram bot"""
        # Create application
        self.application = Application.builder().token(self.telegram_token).build()
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("check", self.check_command))
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        # Start the bot
        logger.info("Starting Bakong Transaction Status Bot...")
        self.application.run_polling(allowed_updates=Update.ALL_TYPES)

def main():
    """Main function to run the bot"""
    # Configuration - Edit these values directly in the code
    telegram_token = "YOUR_TELEGRAM_BOT_TOKEN_HERE"  # Replace with your bot token from @BotFather
    bakong_bearer_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k"

    # Fallback to environment variables if not set above
    if telegram_token == "YOUR_TELEGRAM_BOT_TOKEN_HERE":
        telegram_token = os.getenv("TELEGRAM_BOT_TOKEN")

    if not telegram_token:
        print("❌ Error: Please set your Telegram bot token!")
        print("\n🔧 To set up:")
        print("1. Create a bot with @BotFather on Telegram")
        print("2. Copy your bot token")
        print("3. Edit this file and replace 'YOUR_TELEGRAM_BOT_TOKEN_HERE' with your actual token")
        print("\n✅ Your Bakong bearer token is already configured!")
        print("\n📝 Example:")
        print('   telegram_token = "1234567890:ABCdefGHIjklMNOpqrsTUVwxyz"')
        return
    
    # Create and run bot
    bot = BakongTelegramBot(telegram_token, bakong_bearer_token)
    
    try:
        bot.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {str(e)}")

if __name__ == "__main__":
    main()
