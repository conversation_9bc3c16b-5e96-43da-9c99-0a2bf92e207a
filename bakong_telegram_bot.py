#!/usr/bin/env python3
"""
Bakong Transaction Status Telegram Bot
Checks transaction status using MD5 hash via Bakong API
"""

import os
import logging
import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, 
    CommandHandler, 
    MessageHandler, 
    CallbackQueryHandler,
    ContextTypes,
    filters
)

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BakongAPI:
    """Bakong API client for transaction status checking"""
    
    def __init__(self, base_url: str = "https://api-bakong.nbc.gov.kh", bearer_token: str = None):
        self.base_url = base_url
        self.bearer_token = bearer_token
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def check_transaction_by_md5(self, md5_hash: str) -> Dict[str, Any]:
        """
        Check transaction status by MD5 hash
        
        Args:
            md5_hash: MD5 hash of the transaction
            
        Returns:
            Dict containing transaction status information
        """
        if not self.session:
            raise RuntimeError("API client not initialized. Use async context manager.")
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        if self.bearer_token:
            headers["Authorization"] = f"Bearer {self.bearer_token}"
        
        # Common endpoint patterns for transaction status by MD5
        endpoints_to_try = [
            f"/api/v1/transaction/status/md5/{md5_hash}",
            f"/api/transaction/check/md5/{md5_hash}",
            f"/v1/transaction/status/{md5_hash}",
            f"/api/check-transaction-by-md5/{md5_hash}"
        ]
        
        for endpoint in endpoints_to_try:
            try:
                url = f"{self.base_url}{endpoint}"
                logger.info(f"Trying endpoint: {url}")
                
                async with self.session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "success": True,
                            "data": data,
                            "endpoint_used": endpoint
                        }
                    elif response.status == 404:
                        continue  # Try next endpoint
                    else:
                        error_text = await response.text()
                        logger.warning(f"Endpoint {endpoint} returned {response.status}: {error_text}")
                        
            except Exception as e:
                logger.error(f"Error trying endpoint {endpoint}: {str(e)}")
                continue
        
        return {
            "success": False,
            "error": "Transaction not found or API endpoint not accessible",
            "status_code": None
        }

class BakongTelegramBot:
    """Telegram bot for Bakong transaction status checking"""
    
    def __init__(self, telegram_token: str, bakong_bearer_token: str = None):
        self.telegram_token = telegram_token
        self.bakong_bearer_token = bakong_bearer_token
        self.application = None
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = (
            "🏦 *Bakong Transaction Status Bot*\n\n"
            "Welcome! I can help you check transaction status using MD5 hash.\n\n"
            "*Commands:*\n"
            "• `/start` - Show this welcome message\n"
            "• `/check <md5_hash>` - Check transaction status\n"
            "• `/help` - Show help information\n\n"
            "*Usage:*\n"
            "Send me an MD5 hash directly or use the `/check` command followed by the MD5 hash.\n\n"
            "Example: `/check a1b2c3d4e5f6789012345678901234ab`"
        )
        
        keyboard = [
            [InlineKeyboardButton("📋 Help", callback_data="help")],
            [InlineKeyboardButton("🔍 Check Sample", callback_data="sample")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = (
            "🆘 *Help - Bakong Transaction Status Bot*\n\n"
            "*How to use:*\n"
            "1. Send me an MD5 hash (32 characters)\n"
            "2. Or use `/check <md5_hash>`\n\n"
            "*MD5 Hash Format:*\n"
            "• Must be exactly 32 characters\n"
            "• Contains only letters (a-f) and numbers (0-9)\n"
            "• Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
            "*Commands:*\n"
            "• `/start` - Welcome message\n"
            "• `/check <hash>` - Check specific transaction\n"
            "• `/help` - This help message\n\n"
            "💡 *Tip:* You can send the MD5 hash directly without any command!"
        )
        
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def check_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /check command"""
        if not context.args:
            await update.message.reply_text(
                "❌ Please provide an MD5 hash.\n\n"
                "Usage: `/check <md5_hash>`\n"
                "Example: `/check a1b2c3d4e5f6789012345678901234ab`",
                parse_mode='Markdown'
            )
            return
        
        md5_hash = context.args[0].strip()
        await self.process_transaction_check(update, md5_hash)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle direct MD5 hash messages"""
        message_text = update.message.text.strip()
        
        # Check if message looks like an MD5 hash
        if self.is_valid_md5(message_text):
            await self.process_transaction_check(update, message_text)
        else:
            await update.message.reply_text(
                "❌ Invalid MD5 hash format.\n\n"
                "MD5 hash should be:\n"
                "• Exactly 32 characters\n"
                "• Contains only letters (a-f) and numbers (0-9)\n\n"
                "Example: `a1b2c3d4e5f6789012345678901234ab`\n\n"
                "Use `/help` for more information.",
                parse_mode='Markdown'
            )
    
    def is_valid_md5(self, text: str) -> bool:
        """Validate MD5 hash format"""
        if len(text) != 32:
            return False
        
        try:
            int(text, 16)  # Check if it's valid hexadecimal
            return True
        except ValueError:
            return False
    
    async def process_transaction_check(self, update: Update, md5_hash: str):
        """Process transaction status check"""
        if not self.is_valid_md5(md5_hash):
            await update.message.reply_text(
                "❌ Invalid MD5 hash format. Please provide a valid 32-character MD5 hash.",
                parse_mode='Markdown'
            )
            return
        
        # Send "checking" message
        checking_msg = await update.message.reply_text(
            f"🔍 Checking transaction status...\n\n"
            f"MD5: `{md5_hash}`",
            parse_mode='Markdown'
        )
        
        try:
            async with BakongAPI(bearer_token=self.bakong_bearer_token) as api:
                result = await api.check_transaction_by_md5(md5_hash)
                
                if result["success"]:
                    # Format successful response
                    data = result["data"]
                    response_message = (
                        f"✅ *Transaction Found*\n\n"
                        f"🔗 MD5: `{md5_hash}`\n"
                        f"📡 Endpoint: `{result['endpoint_used']}`\n\n"
                        f"📊 *Transaction Details:*\n"
                        f"```json\n{json.dumps(data, indent=2, ensure_ascii=False)}\n```"
                    )
                else:
                    # Format error response
                    response_message = (
                        f"❌ *Transaction Not Found*\n\n"
                        f"🔗 MD5: `{md5_hash}`\n\n"
                        f"💡 *Possible reasons:*\n"
                        f"• Transaction doesn't exist\n"
                        f"• Invalid MD5 hash\n"
                        f"• API endpoint not accessible\n"
                        f"• Authentication issues\n\n"
                        f"Error: {result.get('error', 'Unknown error')}"
                    )
                
                await checking_msg.edit_text(response_message, parse_mode='Markdown')
                
        except Exception as e:
            error_message = (
                f"🚨 *Error Occurred*\n\n"
                f"🔗 MD5: `{md5_hash}`\n\n"
                f"❌ Error: {str(e)}\n\n"
                f"Please try again later or contact support."
            )
            await checking_msg.edit_text(error_message, parse_mode='Markdown')
            logger.error(f"Error checking transaction {md5_hash}: {str(e)}")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button callbacks"""
        query = update.callback_query
        await query.answer()
        
        if query.data == "help":
            await self.help_command(update, context)
        elif query.data == "sample":
            sample_message = (
                "📝 *Sample MD5 Hash*\n\n"
                "Here's a sample MD5 hash format:\n"
                "`a1b2c3d4e5f6789012345678901234ab`\n\n"
                "You can copy this and modify it, or use your actual transaction MD5 hash.\n\n"
                "💡 Just send the MD5 hash directly to check!"
            )
            await query.edit_message_text(sample_message, parse_mode='Markdown')
    
    def run(self):
        """Run the Telegram bot"""
        # Create application
        self.application = Application.builder().token(self.telegram_token).build()
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("check", self.check_command))
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        # Start the bot
        logger.info("Starting Bakong Transaction Status Bot...")
        self.application.run_polling(allowed_updates=Update.ALL_TYPES)

def main():
    """Main function to run the bot"""
    # Get tokens from environment variables
    telegram_token = os.getenv("TELEGRAM_BOT_TOKEN")
    bakong_bearer_token = os.getenv("BAKONG_BEARER_TOKEN")  # Optional
    
    if not telegram_token:
        print("❌ Error: TELEGRAM_BOT_TOKEN environment variable is required!")
        print("\nTo set up:")
        print("1. Create a bot with @BotFather on Telegram")
        print("2. Set environment variable: TELEGRAM_BOT_TOKEN=your_bot_token")
        print("3. Optionally set: BAKONG_BEARER_TOKEN=your_bearer_token")
        return
    
    # Create and run bot
    bot = BakongTelegramBot(telegram_token, bakong_bearer_token)
    
    try:
        bot.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {str(e)}")

if __name__ == "__main__":
    main()
